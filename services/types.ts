// Types for service layer responses

export interface CustomerPointsSummary {
  totalEarned: number;
  totalActive: number;
  totalRedeemed: number;
}

export interface CustomerTransaction {
  id: number;
  type: 'purchase' | 'redemption';
  businessName: string;
  businessCategory: string;
  pointsEarned: number;
  pointsRedeemed: number;
  amount: string;
  date: string;
}

export interface CustomerBusinessSummary {
  id: number;
  name: string;
  category: string;
  points: number;
  lastVisit: string;
}

export interface ServiceResponse<T> {
  data: T | null;
  error: string | null;
}
