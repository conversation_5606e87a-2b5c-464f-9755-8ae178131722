import { supabase } from '@/lib/supabase';
import {
  CustomerTransaction,
  CustomerBusinessSummary,
  ServiceResponse,
} from './types';

/**
 * Transactions service for handling customer transaction and business history operations
 */
export class TransactionsService {
  /**
   * Get customer transaction history including purchases and redemptions
   * @param customerId - The customer's UUID
   * @returns Promise with transaction history data or error
   */
  static async getCustomerTransactionHistory(
    customerId: string
  ): Promise<ServiceResponse<CustomerTransaction[]>> {
    try {
      const { data, error } = await supabase.rpc(
        'get_customer_transaction_history',
        {
          p_customer_id: customerId,
          p_page: 1,
          p_page_size: 100,
        }
      );

      if (error) {
        console.error('Error fetching customer transaction history:', error);
        return {
          data: null,
          error: error.message || 'Failed to fetch transaction history',
        };
      }

      if (!data || !Array.isArray(data)) {
        return {
          data: [],
          error: null,
        };
      }

      // Map the database response to our expected format
      const transactions: CustomerTransaction[] = data.map((item: any) => ({
        id: item.transaction_id,
        type: item.transaction_type.toLowerCase(),
        businessName: item.business_name,
        businessCategory: item.business_type,
        pointsEarned: item.points_awarded,
        pointsRedeemed: item.points_redeemed,
        amount: item.amount_spent,
        date: item.created_at,
      }));

      return {
        data: transactions,
        error: null,
      };
    } catch (error) {
      console.error(
        'Unexpected error in getCustomerTransactionHistory:',
        error
      );
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      };
    }
  }

  /**
   * Get customer business summaries including points earned per business
   * @param customerId - The customer's UUID
   * @returns Promise with business summaries data or error
   */
  static async getCustomerBusinessSummaries(
    customerId: string
  ): Promise<ServiceResponse<CustomerBusinessSummary[]>> {
    try {
      const { data, error } = await supabase.rpc(
        'get_customer_business_summaries',
        {
          p_customer_id: customerId,
        }
      );

      if (error) {
        console.error('Error fetching customer business summaries:', error);
        return {
          data: null,
          error: error.message || 'Failed to fetch business summaries',
        };
      }

      if (!data || !Array.isArray(data)) {
        return {
          data: [],
          error: null,
        };
      }

      // Map the database response to our expected format
      const businesses: CustomerBusinessSummary[] = data.map((item: any) => ({
        id: item.business_id,
        name: item.business_name,
        category: item.business_type,
        points: item.active_points,
        lastVisit: item.last_transaction_date,
      }));

      return {
        data: businesses,
        error: null,
      };
    } catch (error) {
      console.error('Unexpected error in getCustomerBusinessSummaries:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      };
    }
  }
}
